"use client";

import { UserButton } from "@/features/auth/components/user-button";
import { useEffect, useMemo } from "react";

import { useCreateWorkspaceModal } from "@/features/workspaces/store/use-create-workspace-modal";
import { useGetWorkSpaces } from "@/features/workspaces/api/use-get-workspaces";
import { useRouter } from "next/navigation";

export default function Home() {

  const router = useRouter();

  const [open, setOpen] = useCreateWorkspaceModal();
  const {data, isLoading} = useGetWorkSpaces();
  
  const workspaceId = useMemo(() => data?.[0]?._id, [data]);
  
  useEffect(() => {
    if (isLoading) return;

    if (workspaceId) {
      console.log("Redirect to workspace");
      router.replace(`/workspace/${workspaceId}`);
    } else if (!open) {
      console.log("Open creation modal");
      setOpen(true);
    }

  }, [workspaceId, isLoading, open, setOpen, router]);

  return (
    <div>
      <UserButton />
    </div>
  );
};