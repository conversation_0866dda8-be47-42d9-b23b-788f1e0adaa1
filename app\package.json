{"name": "app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/core": "^0.37.4", "@convex-dev/auth": "^0.0.80", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.18.2", "date-fns": "^4.1.0", "jotai": "^2.11.0", "lucide-react": "^0.471.1", "next": "15.1.4", "next-themes": "^0.4.4", "nuqs": "^2.4.0", "quill": "^2.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.4.0", "react-resizable-panels": "^2.1.7", "react-use": "^17.6.0", "react-verification-input": "^4.2.0", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8", "tailwindcss": "^3.4.1", "eslint": "^9", "eslint-config-next": "15.1.4", "@eslint/eslintrc": "^3"}}