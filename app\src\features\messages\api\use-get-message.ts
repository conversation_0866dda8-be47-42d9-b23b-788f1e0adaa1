import { useQuery } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { Id } from "../../../../convex/_generated/dataModel";

// workspaces can be members or any table in the schema, contains direct connection to table.
interface useGetMessageProps {
  id: Id<"messages">; 
}

export const useGetMessage = ({ id }: useGetMessageProps) => {
  const data = useQuery(api.messages.getById, { id });
  const isLoading = data === undefined;

  return { data, isLoading };
}

