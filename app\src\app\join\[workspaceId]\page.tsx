"use client";

import { useMemo, useEffect } from "react";
import VerificationInput from "react-verification-input"
import { But<PERSON> } from "@/components/ui/button";
import { Loader } from "lucide-react";
import { toast } from "sonner";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";

import { useGetWorkSpaceInfo } from "@/features/workspaces/api/use-get-workspace-info";
import { useWorkspaceId } from "@/hooks/use-workspace-id";
import { useJoin } from "@/features/workspaces/api/use-join";

// page.tsx file always requires default export
// you can use interface to get parameter id or use useWorkspaceId() hook to get id

// interface JoinPageProps {
//  params: {
//    workspaceId: string;
//  };
//};

const JoinPage = () => {

  const router = useRouter();
  const workspaceId = useWorkspaceId();
  
  const { mutate, isPending } = useJoin();
  const { data, isLoading } = useGetWorkSpaceInfo({ id: workspaceId });

  const isMember = useMemo(() => data?.isMember, [data?.isMember]);

  useEffect(() => {
    if (isMember) {
      router.push(`/workspace/${workspaceId}`);
    }
  }, [isMember, router, workspaceId]);

  const handleComplete = (value: string) => {
    mutate({ workspaceId, joinCode: value }, {
      onSuccess: (id) => {
        // router.push will not allow user to return to the previous page
        // router.replace will allow user to go back on the previous page
        router.replace(`/workspace/${id}`);
        toast.success("Workspace joined");
      },
      onError: () => {
        toast.error("Failed to join workspace")
      }
    })
  };

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <Loader className="size-6 animate-spin text-muted-foreground" />
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col gap-y-8 items-center justify-center bg-white p-8 rounded-lg shadow-md">

      <Image src="/nike-logo.svg" width={60} height={60} alt="Nike Logo" />

      <div className="flex flex-col gap-y-4 items-center justify-center max-w-md">
        <div className="flex flex-col gap-y-2 items-center justify-center">
          <h1 className="text-2xl font-bold">
            Join {data?.name}
          </h1>
          <p className="text-md text-muted-foreground">
            Enter the workspace code to join
          </p>
        </div> 

        <VerificationInput 
          onComplete={handleComplete}
          length={6}
          classNames={{
            container: cn("flex gap-x-2", isPending && "opacity-50 cursor-not-allowed"),
            character: "uppercase h-auto rounded-md border border-gray-300 flex items-center justify-center text-lg font-medium text-gray-500",
            characterInactive: "bg-muted",
            characterSelected: "bg-white text-black",
            characterFilled: "bg-white text-black"
          }} 
          autoFocus
        />
      </div>

      <div className="flex gap-x-4">
          <Button
            size="lg"
            variant="outline"
            asChild
          >
            <Link href="/">
              Back to home
            </Link>
          </Button>
      </div>

    </div>
  );
};

export default JoinPage;