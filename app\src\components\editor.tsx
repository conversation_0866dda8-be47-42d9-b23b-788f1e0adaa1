import Quill, { QuillOptions } from "quill";
import { Delta, Op } from "quill/core";
import { MdSend } from "react-icons/md";
import { RefObject, useEffect, useLayoutEffect, useRef, useState } from "react";
import { <PERSON><PERSON> } from "./ui/button";
import { PiTextAa } from "react-icons/pi";
import { ImageIcon, Smile, XIcon } from "lucide-react";
import { Hint } from "./hint";
import { cn } from "@/lib/utils";
import { EmojiPopover } from "./emoji-popover";
import Image from "next/image";

import "quill/dist/quill.snow.css";

// Note# Quill.js doesnt support serverside rendering.

type EditorValue = {
  image: File | null;
  body: string;
};

interface EditorProps {
  onSubmit: ({ image, body }: EditorValue) => void;
  onCancel?: () => void;
  placeholder?: string;
  defaultValue?: Delta | Op[];
  disabled?: boolean;
  innerRef?: RefObject<Quill | null>;
  variant?: "create" | "update";
};

const Editor = ({ 
  onCancel,
  onSubmit,
  placeholder = "Write something...",
  defaultValue = [],
  disabled = false,
  innerRef,
  variant = "create"
}: EditorProps) => {

  const [text, setText] = useState("");
  const [image, setImage] = useState<File | null>(null);
  const [isToolbarVisible, setIsToolbarVisible] = useState(true);

  // This component heavily relies on refs, useRef hook in React is designed to persist values 
  // across renders without causing re-renders when the value changes. This is because useRef returns 
  // a mutable object with a .current property, and changes to .current do not trigger a re-render.
  // It also does not require to place it on the dependency array.

  const submitRef = useRef(onSubmit);
  const placeholderRef = useRef(placeholder);
  const quillRef = useRef<Quill | null>(null);
  const defaultValueRef = useRef(defaultValue);
  const containerRef = useRef<HTMLDivElement>(null); // Wields the entire quill text editor
  const disabledRef = useRef(disabled);
  const imageElementRef = useRef<HTMLInputElement>(null);

  // useLayoutEffct()
  // Runs synchronously after the DOM is updated but before the browser paints.
  // Ideal for DOM measurements or adjustments that must happen before the user sees the screen.
  // Use when you need to perform DOM measurements or adjustments that must happen before the browser paints.
  // Example: Measuring element sizes, scrolling to a position, or synchronously updating the DOM.

  useLayoutEffect(() => {
    submitRef.current = onSubmit;
    placeholderRef.current = placeholder;
    defaultValueRef.current = defaultValue;
    disabledRef.current = disabled;
  });

  // useEffect()
  // Runs asynchronously after the browser has painted the screen.
  // Ideal for side effects that don't need to block rendering, like data fetching or subscriptions.
  // Use for side effects that don't need to block rendering, such as data fetching, subscriptions, or updating state based on props.
  // Example: Fetching data from an API or setting up event listeners.

  useEffect(() => {
    // Note # refs are meant to be used inside useEffect the rest should be used on a standard way

    if (!containerRef.current) return;

    const container = containerRef.current;
    const editorContainer = container.appendChild(
      container.ownerDocument.createElement("div"),
    );
    
    // Quill options based on the documentation
    const options: QuillOptions = {
      theme: "snow",   
      placeholder: placeholderRef.current,
      modules: {
        toolbar: [
          ["bold", "italic", "strike"],
          ["link"],
          [{ list: "ordered" }, { list: "bullet" }]
        ],
        keyboard: {
          bindings: {
            enter: {
              key: "Enter",
              handler: () => {
                const text = quill.getText();
                const addedImage = imageElementRef.current?.files?.[0] || null;

                const isEmpty = !addedImage && text.replace(/<(.|\n)*?>/g, "").trim().length === 0;

                if (isEmpty) return;

                const body = JSON.stringify(quill.getContents());
                // Return body & image to handlesubmit in chat-input.tsx
                submitRef.current?.({ body, image: addedImage })

              }
            },
            shift_enter: {
              key: "Enter",
              shiftKey: true,
              handler: () => {
                quill.insertText(quill.getSelection()?.index || 0, "\n");
              }
            }
          }
        }
      }
    };

    // Initialize quill
    const quill = new Quill(editorContainer, options);
    quillRef.current = quill;
    
    // Focus on the component upon loading
    quillRef.current.focus();

    if (innerRef) {
      // has the same functionality with "quillRef = quill;""
      innerRef.current = quill;
    }

    // Set quill default text details
    quill.setContents(defaultValueRef.current);
    setText(quill.getText());

    // Set new data from text editor to setText like onChange
    quill.on(Quill.events.TEXT_CHANGE, () => {
      setText(quill.getText());
    });

    return () => {

      // Clean up for quill
      quill.off(Quill.events.TEXT_CHANGE);

      // Clean up for container append
      if (container) {
        container.innerHTML = "";
      }

      // Clean up detach focus function to the text editor
      if (quillRef.current) {
        quillRef.current = null;
      }

      // Clean up detach the quill from the component
      if (innerRef) {
        innerRef.current = null;
      }

    };

  }, [innerRef]);

  const toggleToolbar = () => {
    setIsToolbarVisible((current) => !current);
    const toolbarElement = containerRef.current?.querySelector(".ql-toolbar");

    if (toolbarElement) {
      toolbarElement.classList.toggle("hidden");
    }
  };

  const onEmojiSelect = (emoji: any) => {
    const quill = quillRef.current;

    quill?.insertText(quill?.getSelection()?.index || 0, emoji.native)
  }

  // Note# by default, quill text editor contains random element like <br /> or <p></p>, 
  // placing regex validation will remove everything
  const isEmpty = !image && text.replace(/<(.|\n)*?>/g, "").trim().length === 0;

  return (
    <div className="flex flex-col">

      
      <input // Use input field to upload image and it is hidden, will only be trigger by image logo. 
        type="file"
        accept="image/*"
        ref={imageElementRef}
        onChange={(event) => setImage(event.target.files![0])}
        className="hidden"
      />

      <div className={ cn("flex flex-col border border-slate-200 rounded-md overflow-hidden focus-within:border-slate-300 focus-within:shadow-sm transition bg-white", disabled && "opacity-50") }>
        <div ref={containerRef} className="h-full ql-custom" />
        
        {!! image && ( // Selected image will be retained here together with text.
          <div className="p-2">
            <div className="relative size-[62px] flex items-start justify-center group/image">
              <Hint label="Remove Image">
                <button
                  onClick={ () => {
                    setImage(null);
                    imageElementRef.current!.value="";
                  }}
                  className="hidden group-hover/image:flex rounded-full bg-black/70 hover:bg-black absolute -top-2.5 -right-2.5 text-white size-6 z-[4] border-2 border-white items-center justify-center"
                >
                  <XIcon className="size-3.5" />
                </button>
              </Hint>

              <Image
                src={URL.createObjectURL(image)}
                alt="Uploaded"
                fill 
                className="rounded-xl overflow-hidden border object-cover"
              />
            </div>
          </div>
        )}

        <div className="flex px-2 pb-2 z-[5]">

          <Hint label={isToolbarVisible ? "Hide formatting" : "Show formatting"}>
            <Button
              disabled={disabled}
              size="iconSm"
              variant="ghost"
              onClick={toggleToolbar}
            >
              <PiTextAa className="size-4" />
            </Button>
          </Hint>

          <EmojiPopover onEmojiSelect={onEmojiSelect}>
            <Button
              disabled={disabled}
              size="iconSm"
              variant="ghost"
            >
              <Smile className="size-4" />
            </Button>
          </EmojiPopover>

          {variant === "create" && (
            <Hint label="Image">
              <Button
                disabled={disabled}
                size="iconSm"
                variant="ghost"
                onClick={() => imageElementRef.current?.click()}
              >
                <ImageIcon className="size-4" />
              </Button>
            </Hint>
          )}

          {variant === "update" && (
            <div className="ml-auto flex items-center gap-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onCancel}
                disabled={disabled}
              >
                Cancel
              </Button>
              <Button
                disabled={disabled || isEmpty}
                onClick={() => {
                  onSubmit({
                    body: JSON.stringify(quillRef.current?.getContents()),
                    image,
                  })
                }}
                size="sm"
                className="bg-[#007a5a] hover:bg-[#007a5a]/80 text-white"
              >
                Save
              </Button>
            </div>
          )}

          {variant === "create" && (
            <Button 
              disabled={disabled || isEmpty}
              onClick={() => {
                onSubmit({
                  body: JSON.stringify(quillRef.current?.getContents()),
                  image,
                })
              }}
              size="iconSm"
              className={cn(
                "ml-auto",
                isEmpty
                ? "bg-white hover:bg-white text-muted-foreground"
                : "bg-[#007a5a] hover:bg-[#007a5a]/80 text-white"
              )}
            >
              <MdSend className="size-4" />
            </Button>
          )}

        </div>
      </div>

      {variant === "create" && (
        <div className={cn(
          "p-2 text-[10px] text-muted-foreground flex justify-end opacity-0 transition",
          !isEmpty && "opacity-100"
        )}>
          <p>
            <strong>Shift + Return</strong> to add a new line.
          </p>
        </div>
      )}
      
    </div>
  );
};

export default Editor;