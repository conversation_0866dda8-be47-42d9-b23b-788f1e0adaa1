import { useWorkspaceId } from "@/hooks/use-workspace-id";
import { useChannelId } from "@/hooks/use-channel-id";

import { useCurrentMember } from "@/features/members/api/use-current-members";
import { useGetMembers } from "@/features/members/api/use-get-members";
import { useGetWorkSpace } from "@/features/workspaces/api/use-get-workspace";
import { useGetChannels } from "@/features/channels/api/use-get-channels";
import { useCreateChannelModal } from "@/features/channels/store/use-create-channel-modal";

import { WorkspaceSection } from "./workspace-section";
import { WorkspaceHeader } from "./workspace-header";

import { SidebarItem } from "./sidebar-item";
import { UserItem } from "./user-item";

import { AlertTriangle, HashIcon, Loader, MessageSquareText, SendHorizontal } from "lucide-react";

export const WorkspaceSidebar = () => {
  const workspaceId = useWorkspaceId(); 
  const channelId = useChannelId();

  const [open, setOpen] = useCreateChannelModal();

  const { data: member, isLoading: memberLoading } = useCurrentMember({ workspaceId }); // get the logged in user
  const { data: members, isLoading: membersLoading } = useGetMembers({ workspaceId });

  const { data: workspace, isLoading: workspaceLoading } = useGetWorkSpace({ id: workspaceId }); // load specific workspace
  const { data: channels, isLoading: channelsLoading  } = useGetChannels({ workspaceId });

  if ( workspaceLoading || memberLoading ) {
    return (
      <div className="flex flex-col bg-[#5E2C5F] h-full items-center justify-center">
        <Loader className="size-5 animate-spin text-white" /> 
      </div>
    );
  }

  if ( !workspace || !member ) {
    return (
      <div className="flex flex-col bg-[#5E2C5F] h-full items-center justify-center">
        <AlertTriangle className="size-5 text-white" />
        <p className="text-white text-sm">
          Workspace not found
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-col bg-[#5E2C5F] h-full">
        
        <WorkspaceHeader workspace={workspace} isAdmin={member.role === "admin"} />
        
        <div className="flex flex-col px-2 mt-3">
          <SidebarItem
            label="Threads"
            icon={MessageSquareText}
            id="threads"
          />
          <SidebarItem
            label="Drafts & Sent"
            icon={SendHorizontal}
            id="drafts"
          />
        </div>

        <WorkspaceSection
          label="Channels"
          hint="New channel"
          onNew={member.role === "admin" ? () => setOpen(true) : undefined}
        >
          {channels?.map((item) => (
            <SidebarItem
              key={item._id}
              icon={HashIcon}
              label={item.name}
              id={item._id}
              variant={channelId === item._id ? "active" : "default"}
            />
          ))}
        </WorkspaceSection>
        
        <WorkspaceSection
          label="Direct Messages"
          hint="New direct message"
          onNew={() => {}}
        >
          {members?.map((item) => (
            <UserItem
              key={item._id}
              id={item._id}
              label={item.user.name}
              image={item.user.image}
            />
          ))}
        </WorkspaceSection>

    </div>
  )
};