"use client";
// (note) The page is interactive, Majority of components are using on client side more than serverside.
// (note) Passing a "use client or use server" on a route doesnt mean "{children}" is also passing the "use client or use server" boundary.
// (note) You can place server components inside of "use client" components by using {children} props, As long as the components are passed by {children} it will not be a "use client" component by default.

import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";

import { Sidebar } from "./sidebar";
import { Toolbar } from "./toolbar";
import { usePanel } from "@/hooks/use-panel";
import { WorkspaceSidebar } from "./workspace-sidebar";
import { Loader } from "lucide-react";
import { Thread } from "@/features/messages/components/thread";
import { Id } from "../../../../convex/_generated/dataModel";

interface WorkspaceIdLayoutProps {
  children: React.ReactNode;
};

const WorkspaceLayout = ({ children }: WorkspaceIdLayoutProps) => {

  const { parentMessageId, onClose } = usePanel();

  const showPanel = !!parentMessageId;  

  return (
    <div className="h-full">
      <Toolbar />
      
      <div className="flex h-[calc(100vh-40px)]">
        <Sidebar />
        
        <ResizablePanelGroup 
          direction="horizontal" 
          autoSaveId="ca-workspace-layout"
        >
          <ResizablePanel
            defaultSize={20}
            minSize={11}
            className="bg-[#5E2C5F]"
          >
            <WorkspaceSidebar />
          </ResizablePanel>
          
          <ResizableHandle withHandle />
          
          <ResizablePanel minSize={20}>
            {children}
          </ResizablePanel>

          {showPanel && (
            <>
              <ResizableHandle withHandle />
              <ResizablePanel minSize={20} defaultSize={29}>
                {parentMessageId ? 
                  (
                    <Thread
                      messageId={parentMessageId as Id<"messages">}
                      onClose={onClose}
                    />
                  ) : (
                    <div className="flex h-full items-center justify-center">
                      <Loader className="size-5 animate-spin text-muted-foreground" />
                    </div>
                  )
                }
              </ResizablePanel>
            </>
          )}

        </ResizablePanelGroup>
      </div>
    </div>
  );
}

export default WorkspaceLayout;