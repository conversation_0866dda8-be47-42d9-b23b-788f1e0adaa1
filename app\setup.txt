# Requirements
  Next js - https://nextjs.org/
  Node Js - https://nodejs.org/en

  Bun - https://bun.sh/

  Tailwindcss - https://tailwindcss.com/
  Shadcn Ui - https://ui.shadcn.com/

  Convex - https://www.convex.dev/
  Convex OAuth - https://labs.convex.dev/auth/config/oauth

# Install Bun For Package Manager
  npm install -g bun

# Run "bunx create next-app@latest app"
  √ Would you like to use TypeScript? ... No / -Yes
  √ Would you like to use ESLint? ... No / -Yes
  √ Would you like to use Tailwind CSS? ... No / -Yes
  √ Would you like your code inside a `src/` directory? ... No / -Yes
  √ Would you like to use App Router? (recommended) ... No / -Yes
  √ Would you like to use Turbopack for `next dev`? ... No / -Yes
  √ Would you like to customize the import alias (`@/*` by default)? ... -No / Yes

# Install Shadcn Ui for Next Js using Bun
  -To install and initialize
    bun x --bun shadcn@latest init
  -To add component
    bun x --bun shadcn@latest add "component name"

# Add React Icons
  bun add react-icons

# Run The App
  
  -Change directory to the app folder
   cd app
  
  -Run the application using any of the command
   bun run dev
   npm run dev

  -Run the backend ( After Convex Installation )
   bunx convex dev 

# Update globals.css 
  html,
  body, 
  :root {
    height: 100%;
  }

# Update tailwind.config.ts content
  "./pages/**/*.{js,ts,jsx,tsx,mdx}",
  "./components/**/*.{js,ts,jsx,tsx,mdx}",
  "./app/**/*.{js,ts,jsx,tsx,mdx}",
  "./src/**/*.{js,ts,jsx,tsx,mdx}",

# Add Convex ( Relational Database & Query )
  bun add convex

  Run Convex
    bunx convex dev

    # Command Line Generated
    Welcome to developing with Convex, let's get you logged in.
    ? Device name: tvp-app
    Visit https://auth.convex.dev/activate?user_code=HQLR-WRXB to finish logging in.
    You should see the following code which expires in 15 minutes: HQLR-WRXB        
    ? Open the browser? Yes
    ✔ Saved credentials to C:\Users\<USER>\.convex\config.json
    ? Do you agree to the Terms of Service at https://convex.dev/legal/v2022-03-02/tos Yes
    ? Project name: app
    ✔ Created project app-0e2f8, manage it at https://dashboard.convex.dev/t/vincent-canje/app-0e2f8
  
  # To save json data, create a json file inside your application folder then run
    bunx convex import --table tasks filename.jsonl
  
  # To install convex authentication
    bun add @convex-dev/auth @auth/core
  
  # To run install convex authentication
    bun x @convex-dev/auth

    ✖ There are unstaged or uncommitted changes in the working directory. Please commit or stash them before proceeding.
    ? Continue anyway? Yes      
    i Step 1: Configure SITE_URL
    ? Enter the URL of your local web server (e.g. http://localhost:1234) http://localhost:3000
    ✔ Successfully set SITE_URL to http://localhost:3000 (on dev deployment pleasant-kangaroo-699)

    i Step 2: Configure private and public key
    ✔ Successfully set JWT_PRIVATE_KEY (on dev deployment pleasant-kangaroo-699)
    ✔ Successfully set JWKS (on dev deployment pleasant-kangaroo-699)

    i Step 3: Modify tsconfig file
    ✔ The convex\tsconfig.json is already set up.

    i Step 4: Configure auth config file
    ✔ Created convex\auth.config.ts

    i Step 5: Initialize auth file
    ✔ Created convex\auth.ts

    i Step 6: Configure http file
    ✔ Created convex\http.ts
    ✔ You're all set. Continue by configuring your schema and frontend.

# Add Jotai ( State Managemer )
  bun add jotai

# Add Collection of hooks
  bun add react-use

# Add React Verification
  bun add react-verification-input

# Add quill editor
  bun add quill

# Add emoji on the text editor
  
  # Requires to packages

    bun add @emoji-mart/data

    bun add @emoji-mart/react  

# Add dates formatting

  bun add date-fns

# Add nuqs for creating threads, to automatically synchronize states on the url

  bun add nuqs

# How to push on github ?
  # If new project
    -git init
    -git add .
    -git commit -m "Initial commit"
    -git remote add origin https://github.com/canjevincent/cms-booking-chat-nextjs.git
    -git push origin master

  # If existing project
    -git add . if to add all new files or use name of the specific file
    -git commit -m "Short description"
    -git push origin master

-- Last 4:08:59 part 2